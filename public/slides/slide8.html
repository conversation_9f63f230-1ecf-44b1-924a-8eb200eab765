<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 8: Timeline & Readiness</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
            --red: #DC3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, var(--blue) 50%, var(--purple) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }


        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 2rem 1.5rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--blue) 50%, var(--purple) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-align: center;
        }

        .milestones-section {
            margin-bottom: 1.5rem;
        }

        .milestones-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .milestone-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: var(--light-gray);
            border-radius: 0.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
        }

        .milestone-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(37, 211, 102, 0.15);
        }

        .milestone-icon {
            font-size: 1.25rem;
            margin-right: 0.5rem;
            min-width: 25px;
        }

        .milestone-text {
            font-size: 0.8rem;
            color: var(--dark);
            font-weight: 500;
            line-height: 1.3;
        }

        .timeline-section {
            margin-bottom: 1.5rem;
        }

        .timeline-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .timeline-card {
            background: var(--light-gray);
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
        }

        .timeline-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(37, 211, 102, 0.15);
        }

        .timeline-period {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.25rem;
        }

        .timeline-activity {
            font-size: 0.8rem;
            color: var(--dark);
            line-height: 1.3;
        }

        .tagline-box {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 0.5rem;
            padding: 1rem;
            text-align: center;
        }

        .tagline-box p {
            font-size: 1rem;
            color: var(--dark);
            margin: 0;
            font-style: italic;
            line-height: 1.3;
        }

        .tagline-box i {
            color: var(--primary);
            margin: 0 0.25rem;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1.5rem 1rem;
            }

            h2 {
                font-size: 1.6rem;
                margin-bottom: 1rem;
            }

            .section-title {
                font-size: 1rem;
                margin-bottom: 0.75rem;
            }

            .milestones-grid,
            .timeline-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .milestone-item,
            .timeline-card {
                padding: 0.5rem;
            }

            .milestone-text,
            .timeline-activity {
                font-size: 0.75rem;
            }

            .timeline-period {
                font-size: 0.8rem;
            }

            .milestones-section,
            .timeline-section {
                margin-bottom: 1rem;
            }

            .tagline-box {
                padding: 0.75rem;
            }

            .tagline-box p {
                font-size: 0.9rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Product Readiness</div>

            <h2>Ready to Launch in 1 Week</h2>

            <div class="milestones-section">
                <h3 class="section-title">Milestones Achieved</h3>
                <div class="milestones-grid">
                    <div class="milestone-item slide-up" style="animation-delay: 0.1s;">
                        <span class="milestone-icon">✅</span>
                        <span class="milestone-text">Product development complete</span>
                    </div>
                    <div class="milestone-item slide-up" style="animation-delay: 0.2s;">
                        <span class="milestone-icon">✅</span>
                        <span class="milestone-text">7-day free trial funnel live</span>
                    </div>
                    <div class="milestone-item slide-up" style="animation-delay: 0.3s;">
                        <span class="milestone-icon">✅</span>
                        <span class="milestone-text">Influencer list ready</span>
                    </div>
                    <div class="milestone-item slide-up" style="animation-delay: 0.4s;">
                        <span class="milestone-icon">✅</span>
                        <span class="milestone-text">Meta & video ad templates ready</span>
                    </div>
                    <div class="milestone-item slide-up" style="animation-delay: 0.5s;">
                        <span class="milestone-icon">✅</span>
                        <span class="milestone-text">Team structure in place</span>
                    </div>
                </div>
            </div>

            <div class="timeline-section">
                <h3 class="section-title">Launch Timeline</h3>
                <div class="timeline-grid">
                    <div class="timeline-card slide-up" style="animation-delay: 0.6s;">
                        <div class="timeline-period">📆 Week 1</div>
                        <div class="timeline-activity">Paid marketing begins</div>
                    </div>
                    <div class="timeline-card slide-up" style="animation-delay: 0.7s;">
                        <div class="timeline-period">📆 Week 2–4</div>
                        <div class="timeline-activity">5000+ user onboarding target</div>
                    </div>
                    <div class="timeline-card slide-up" style="animation-delay: 0.8s;">
                        <div class="timeline-period">📆 Month 2–3</div>
                        <div class="timeline-activity">Scale ads & partnerships</div>
                    </div>
                    <div class="timeline-card slide-up" style="animation-delay: 0.9s;">
                        <div class="timeline-period">📆 Month 4–6</div>
                        <div class="timeline-activity">Feature expansion + upselling</div>
                    </div>
                </div>
            </div>

            <div class="tagline-box slide-up" style="animation-delay: 1.0s;">
                <p>
                    <i class="fas fa-quote-left"></i>
                    <strong>Not an idea — it's a working engine waiting to fly</strong>
                    <i class="fas fa-quote-right"></i>
                </p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide9.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide7.html';
        }
    </script>
</body>
</html>
