<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 8: Timeline & Readiness</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
            --red: #DC3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, var(--blue) 50%, var(--purple) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }


        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 4rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--blue) 50%, var(--purple) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .timeline-container {
            position: relative;
            margin: 2rem 0;
        }

        .timeline-line {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, var(--primary), var(--blue), var(--purple), var(--orange));
            border-radius: 2px;
        }

        .timeline-items {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            position: relative;
        }

        .timeline-item:nth-child(even) {
            flex-direction: row-reverse;
        }

        .timeline-icon {
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2;
            border: 4px solid rgba(255, 255, 255, 0.2);
        }

        .timeline-icon.primary {
            background: linear-gradient(135deg, var(--primary), var(--blue));
        }

        .timeline-icon.blue {
            background: linear-gradient(135deg, var(--blue), var(--purple));
        }

        .timeline-icon.purple {
            background: linear-gradient(135deg, var(--purple), var(--orange));
        }

        .timeline-icon.orange {
            background: linear-gradient(135deg, var(--orange), var(--red));
        }

        .timeline-content {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            width: 45%;
            transition: all 0.3s ease;
        }

        .timeline-content:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(37, 211, 102, 0.15);
        }

        .timeline-item:nth-child(even) .timeline-content {
            margin-right: auto;
        }

        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: auto;
        }

        .timeline-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .timeline-title.primary { color: var(--primary); }
        .timeline-title.blue { color: var(--blue); }
        .timeline-title.purple { color: var(--purple); }
        .timeline-title.orange { color: var(--orange); }

        .timeline-desc {
            color: var(--gray);
            font-size: 0.9rem;
            margin: 0;
        }

        .launch-content {
            background: rgba(253, 126, 20, 0.1);
            border: 2px solid rgba(253, 126, 20, 0.3);
        }

        .launch-title {
            color: var(--orange);
            font-weight: 600;
        }

        .launch-desc {
            color: var(--dark);
            font-weight: 500;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }

            h2 {
                font-size: 2rem;
            }
            
            .timeline-item {
                flex-direction: column !important;
            }
            
            .timeline-item:nth-child(even) {
                flex-direction: column !important;
            }
            
            .timeline-content {
                width: 100%;
                margin: 0 !important;
                margin-top: 1rem !important;
            }
            
            .timeline-line {
                left: 2rem;
            }
            
            .timeline-icon {
                left: 2rem;
                transform: translateX(-50%);
                width: 3rem;
                height: 3rem;
                font-size: 1.2rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Product Readiness</div>
            
            <h2>Product is Ready to Launch</h2>

            <div class="timeline-container">
                <div class="timeline-line"></div>
                
                <div class="timeline-items">
                    <div class="timeline-item slide-up" style="animation-delay: 0.1s;">
                        <div class="timeline-icon primary">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <h4 class="timeline-title primary">MVP Ready ✅</h4>
                            <p class="timeline-desc">Complete product built and tested</p>
                        </div>
                    </div>

                    <div class="timeline-item slide-up" style="animation-delay: 0.2s;">
                        <div class="timeline-icon blue">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <h4 class="timeline-title blue">7-day Trial Launched ✅</h4>
                            <p class="timeline-desc">Free trial system is live and working</p>
                        </div>
                    </div>

                    <div class="timeline-item slide-up" style="animation-delay: 0.3s;">
                        <div class="timeline-icon purple">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <h4 class="timeline-title purple">Marketing Strategy Ready ✅</h4>
                            <p class="timeline-desc">Influencer list, ad campaigns prepared</p>
                        </div>
                    </div>

                    <div class="timeline-item slide-up" style="animation-delay: 0.4s;">
                        <div class="timeline-icon orange">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="timeline-content launch-content">
                            <h4 class="timeline-title launch-title">Launch in 1 Week! 🚀</h4>
                            <p class="timeline-desc launch-desc">Ready to go live immediately after funding</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide9.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide7.html';
        }
    </script>
</body>
</html>
