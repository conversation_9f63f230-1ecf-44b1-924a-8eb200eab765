<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 3: Our Solution</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, var(--blue) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: white;
        }

        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 4rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
        }

        .feature-section {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 1.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
        }

        .section-title.blue {
            color: var(--blue);
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.1);
        }

        .feature-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--blue));
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-size: 1rem;
        }

        .feature-icon.blue {
            background: linear-gradient(135deg, var(--blue), var(--purple));
        }

        .feature-text {
            color: var(--dark);
            font-weight: 500;
            font-size: 1rem;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }
            
            h2 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .feature-section {
                padding: 1.5rem;
            }
            
            .section-title {
                font-size: 1.3rem;
            }
            
            .feature-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.9rem;
            }
            
            .feature-text {
                font-size: 0.9rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Our Solution</div>
            
            <h2>Whamart – Simple, Powerful, Affordable</h2>

            <div class="features-grid">
                <div class="feature-section slide-up" style="animation-delay: 0.1s;">
                    <h3 class="section-title">Core Features</h3>
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-store"></i>
                            </div>
                            <span class="feature-text">Mini WhatsApp-style Store</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <span class="feature-text">Drag & Drop Chat Flow Builder</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <span class="feature-text">24x7 Automation</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-rupee-sign"></i>
                            </div>
                            <span class="feature-text">UPI-based Instant Payments</span>
                        </div>
                    </div>
                </div>

                <div class="feature-section slide-up" style="animation-delay: 0.2s;">
                    <h3 class="section-title blue">Advanced Features</h3>
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon blue">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <span class="feature-text">Product & Service Catalog</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon blue">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <span class="feature-text">Auto Bill PDF Generator</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon blue">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <span class="feature-text">Verified Blue Tick</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon blue">
                                <i class="fas fa-mobile-screen"></i>
                            </div>
                            <span class="feature-text">No App Required</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide4.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide2.html';
        }
    </script>
</body>
</html>
