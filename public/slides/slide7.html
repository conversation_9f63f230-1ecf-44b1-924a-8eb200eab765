<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 7: Investment & Capital Use</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--orange) 0%, var(--accent) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
        }

        .slide-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
        }

        .content-wrapper {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 2rem;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            max-width: 1200px;
            width: 100%;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .content-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--orange), var(--accent));
            border-radius: 2rem 2rem 0 0;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, var(--orange) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .capital-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
            margin-bottom: 2rem;
        }

        .chart-container {
            background: var(--light-gray);
            border-radius: 1.5rem;
            padding: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
        }

        .chart-placeholder {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: conic-gradient(
                var(--orange) 0deg 144deg,
                var(--blue) 144deg 216deg,
                var(--primary) 216deg 324deg,
                var(--purple) 324deg 360deg
            );
            margin: 0 auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chart-center {
            width: 150px;
            height: 150px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .chart-total {
            font-size: 2rem;
            font-weight: 800;
            color: var(--orange);
        }

        .chart-label {
            font-size: 0.9rem;
            color: var(--gray);
            font-weight: 500;
        }

        .allocation-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .allocation-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
        }

        .allocation-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(37, 211, 102, 0.15);
        }

        .allocation-item.marketing {
            background: rgba(253, 126, 20, 0.1);
            border-color: rgba(253, 126, 20, 0.2);
        }

        .allocation-item.tech {
            background: rgba(7, 123, 255, 0.1);
            border-color: rgba(7, 123, 255, 0.2);
        }

        .allocation-item.team {
            background: rgba(37, 211, 102, 0.1);
            border-color: rgba(37, 211, 102, 0.2);
        }

        .allocation-item.legal {
            background: rgba(111, 66, 193, 0.1);
            border-color: rgba(111, 66, 193, 0.2);
        }

        .allocation-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
        }

        .allocation-icon.marketing { background: var(--orange); }
        .allocation-icon.tech { background: var(--blue); }
        .allocation-icon.team { background: var(--primary); }
        .allocation-icon.legal { background: var(--purple); }

        .allocation-info h4 {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.25rem;
        }

        .allocation-info p {
            color: var(--gray);
            font-size: 0.9rem;
        }

        .launch-highlight {
            background: linear-gradient(135deg, var(--primary), var(--blue));
            padding: 2rem;
            border-radius: 1.5rem;
            text-align: center;
            color: white;
        }

        .launch-highlight i {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .launch-highlight h3 {
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }

        .launch-highlight p {
            margin: 0;
            opacity: 0.9;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }
            
            h2 {
                font-size: 2rem;
            }
            
            .capital-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .chart-placeholder {
                width: 250px;
                height: 250px;
            }
            
            .chart-center {
                width: 120px;
                height: 120px;
            }
            
            .chart-total {
                font-size: 1.5rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Capital Allocation</div>
            
            <h2>How We'll Use ₹10 Lakh Capital</h2>

            <div class="capital-content">
                <div class="chart-container slide-up" style="animation-delay: 0.1s;">
                    <div class="chart-placeholder">
                        <div class="chart-center">
                            <div class="chart-total">₹10L</div>
                            <div class="chart-label">Total Capital</div>
                        </div>
                    </div>
                </div>

                <div class="allocation-list">
                    <div class="allocation-item marketing slide-up" style="animation-delay: 0.2s;">
                        <div class="allocation-icon marketing">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="allocation-info">
                            <h4>Marketing & Ads</h4>
                            <p>₹4L - Meta, YouTube, Influencers</p>
                        </div>
                    </div>

                    <div class="allocation-item tech slide-up" style="animation-delay: 0.3s;">
                        <div class="allocation-icon tech">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="allocation-info">
                            <h4>Tech Infrastructure</h4>
                            <p>₹2L - Servers, Tools, Security</p>
                        </div>
                    </div>

                    <div class="allocation-item team slide-up" style="animation-delay: 0.4s;">
                        <div class="allocation-icon team">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="allocation-info">
                            <h4>Team & Operations</h4>
                            <p>₹3L - Salaries, Office</p>
                        </div>
                    </div>

                    <div class="allocation-item legal slide-up" style="animation-delay: 0.5s;">
                        <div class="allocation-icon legal">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="allocation-info">
                            <h4>Legal & Compliance</h4>
                            <p>₹1L - Legal, Licenses</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="launch-highlight slide-up" style="animation-delay: 0.6s;">
                <i class="fas fa-rocket"></i>
                <h3>Launch in 1 week after funding!</h3>
                <p>Product is ready, team is prepared, go-to-market strategy is finalized</p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide8.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide6.html';
        }
    </script>
</body>
</html>
