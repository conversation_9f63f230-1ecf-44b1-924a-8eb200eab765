<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 7: Investment & Capital Use</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--orange) 0%, var(--accent) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }


        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 2.5rem 2rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2.2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--orange) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .budget-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .chart-container {
            background: var(--light-gray);
            border-radius: 1.5rem;
            padding: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
        }

        .chart-placeholder {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            background: conic-gradient(
                var(--primary) 0deg 90deg,
                var(--blue) 90deg 162deg,
                var(--orange) 162deg 198deg,
                var(--purple) 198deg 234deg,
                var(--accent) 234deg 342deg,
                var(--gray) 342deg 360deg
            );
            margin: 0 auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chart-center {
            width: 150px;
            height: 150px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .chart-total {
            font-size: 2rem;
            font-weight: 800;
            color: var(--orange);
        }

        .chart-label {
            font-size: 0.9rem;
            color: var(--gray);
            font-weight: 500;
        }

        .budget-table {
            background: var(--light-gray);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
        }

        .budget-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .budget-table th,
        .budget-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid rgba(37, 211, 102, 0.1);
            font-size: 0.9rem;
        }

        .budget-table th {
            font-weight: 600;
            color: var(--dark);
            background: rgba(37, 211, 102, 0.1);
        }

        .category-name {
            color: var(--dark);
            font-weight: 500;
        }

        .budget-amount {
            color: var(--primary);
            font-weight: 600;
            text-align: right;
        }

        .summary-section {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
        }

        .summary-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            background: var(--light-gray);
            border-radius: 0.75rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
        }

        .summary-icon {
            font-size: 1.5rem;
            margin-right: 0.75rem;
        }

        .summary-text {
            font-size: 0.9rem;
            color: var(--dark);
            font-weight: 500;
        }



        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem 1rem;
            }

            h2 {
                font-size: 1.8rem;
                margin-bottom: 1.5rem;
            }

            .budget-content {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .chart-placeholder {
                width: 200px;
                height: 200px;
            }

            .chart-center {
                width: 100px;
                height: 100px;
            }

            .chart-total {
                font-size: 1.3rem;
            }

            .budget-table {
                padding: 1rem;
            }

            .budget-table th,
            .budget-table td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }

            .summary-section {
                flex-direction: column;
                gap: 1rem;
                align-items: center;
            }

            .summary-item {
                padding: 0.75rem 1rem;
            }

            .summary-text {
                font-size: 0.8rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Capital Allocation</div>
            
            <h2>How We'll Use ₹10 Lakh Efficiently</h2>

            <div class="budget-content">
                <div class="chart-container slide-up" style="animation-delay: 0.1s;">
                    <div class="chart-placeholder">
                        <div class="chart-center">
                            <div class="chart-total">₹10L</div>
                            <div class="chart-label">Total Budget</div>
                        </div>
                    </div>
                </div>

                <div class="budget-table slide-up" style="animation-delay: 0.2s;">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Budget (₹)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="category-name">Influencer Marketing</td>
                                <td class="budget-amount">2,50,000</td>
                            </tr>
                            <tr>
                                <td class="category-name">Meta & YouTube Ads</td>
                                <td class="budget-amount">2,00,000</td>
                            </tr>
                            <tr>
                                <td class="category-name">City Banner Ads</td>
                                <td class="budget-amount">1,00,000</td>
                            </tr>
                            <tr>
                                <td class="category-name">High-Speed Servers</td>
                                <td class="budget-amount">1,00,000</td>
                            </tr>
                            <tr>
                                <td class="category-name">Team (10 People)</td>
                                <td class="budget-amount">3,00,000</td>
                            </tr>
                            <tr>
                                <td class="category-name">Legal & Admin Costs</td>
                                <td class="budget-amount">50,000</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="summary-section slide-up" style="animation-delay: 0.3s;">
                <div class="summary-item">
                    <span class="summary-icon">📦</span>
                    <span class="summary-text">90% spend = direct user acquisition & operations</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">🎯</span>
                    <span class="summary-text">Goal: First 10,000 active paid users within 6 months</span>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide8.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide6.html';
        }
    </script>
</body>
</html>
