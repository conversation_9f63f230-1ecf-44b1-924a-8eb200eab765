<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 10: Team & Contact</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--blue) 0%, var(--purple) 50%, var(--primary) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }


        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 1.5rem 1rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 6px 16px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 1.8rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--blue) 0%, var(--purple) 50%, var(--primary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .reasons-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .reason-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: var(--light-gray);
            border-radius: 0.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
        }

        .reason-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(37, 211, 102, 0.15);
        }

        .reason-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            min-width: 30px;
        }

        .reason-text {
            font-size: 0.9rem;
            color: var(--dark);
            font-weight: 500;
            line-height: 1.3;
        }

        .tagline-box {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 0.75rem;
            padding: 1.25rem;
            text-align: center;
        }

        .tagline-box p {
            font-size: 1.1rem;
            color: var(--dark);
            margin: 0;
            font-style: italic;
            line-height: 1.4;
        }

        .tagline-box i {
            color: var(--primary);
            margin: 0 0.5rem;
        }



        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1rem 0.75rem;
            }

            h2 {
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }

            .reasons-grid {
                gap: 0.5rem;
                margin-bottom: 1rem;
            }

            .reason-item {
                padding: 0.5rem;
            }

            .reason-text {
                font-size: 0.8rem;
            }

            .tagline-box {
                padding: 1rem;
            }

            .tagline-box p {
                font-size: 1rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Investment Summary</div>

            <h2>Why Whamart is the Right Bet?</h2>

            <div class="reasons-grid">
                <div class="reason-item slide-up" style="animation-delay: 0.1s;">
                    <span class="reason-icon">✅</span>
                    <span class="reason-text">Massive underserved market (8 Cr+ sellers)</span>
                </div>
                <div class="reason-item slide-up" style="animation-delay: 0.2s;">
                    <span class="reason-icon">✅</span>
                    <span class="reason-text">Affordable solution with recurring revenue</span>
                </div>
                <div class="reason-item slide-up" style="animation-delay: 0.3s;">
                    <span class="reason-icon">✅</span>
                    <span class="reason-text">Zero dependency on external tools or apps</span>
                </div>
                <div class="reason-item slide-up" style="animation-delay: 0.4s;">
                    <span class="reason-icon">✅</span>
                    <span class="reason-text">Ready-to-launch, team-ready, assets-ready</span>
                </div>
                <div class="reason-item slide-up" style="animation-delay: 0.5s;">
                    <span class="reason-icon">✅</span>
                    <span class="reason-text">Scalable PAN India with regional language support</span>
                </div>
                <div class="reason-item slide-up" style="animation-delay: 0.6s;">
                    <span class="reason-icon">✅</span>
                    <span class="reason-text">Social impact: helping India's smallest entrepreneurs digitize</span>
                </div>
                <div class="reason-item slide-up" style="animation-delay: 0.7s;">
                    <span class="reason-icon">✅</span>
                    <span class="reason-text">Clear exit + monthly investor updates</span>
                </div>
            </div>

            <div class="tagline-box slide-up" style="animation-delay: 0.8s;">
                <p>
                    <i class="fas fa-quote-left"></i>
                    <strong>Back a made-in-India product built for India's growth</strong>
                    <i class="fas fa-quote-right"></i>
                </p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide11.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide9.html';
        }
    </script>
</body>
</html>
