<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 9: Investment Offer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--accent) 0%, var(--primary) 50%, var(--blue) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }


        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 4rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, var(--accent) 0%, var(--primary) 50%, var(--blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .investment-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
            margin-bottom: 2rem;
        }

        .investment-details {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .detail-card {
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
            transition: all 0.3s ease;
        }

        .detail-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(37, 211, 102, 0.15);
        }

        .detail-card.primary {
            background: rgba(37, 211, 102, 0.1);
        }

        .detail-card.blue {
            background: rgba(7, 123, 255, 0.1);
            border-color: rgba(7, 123, 255, 0.2);
        }

        .detail-card.purple {
            background: rgba(111, 66, 193, 0.1);
            border-color: rgba(111, 66, 193, 0.2);
        }

        .detail-card.orange {
            background: rgba(253, 126, 20, 0.1);
            border-color: rgba(253, 126, 20, 0.2);
        }

        .detail-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .detail-title.primary { color: var(--primary); }
        .detail-title.blue { color: var(--blue); }
        .detail-title.purple { color: var(--purple); }
        .detail-title.orange { color: var(--orange); }

        .detail-value {
            color: var(--dark);
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }

        .chart-container {
            background: var(--light-gray);
            border-radius: 1.5rem;
            padding: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
        }

        .roi-chart {
            width: 300px;
            height: 300px;
            border-radius: 1rem;
            background: linear-gradient(45deg, 
                var(--primary) 0% 30%, 
                var(--blue) 30% 60%, 
                var(--purple) 60% 100%);
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .why-invest {
            background: linear-gradient(135deg, var(--primary), var(--blue));
            padding: 2rem;
            border-radius: 1.5rem;
            text-align: center;
            color: white;
            margin-top: 2rem;
        }

        .why-invest h3 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-top: 1rem;
        }

        .benefit-item {
            text-align: center;
        }

        .benefit-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }

        .benefit-text {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }

            h2 {
                font-size: 2rem;
            }
            
            .investment-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .roi-chart {
                width: 250px;
                height: 250px;
                font-size: 1.2rem;
            }
            
            .benefits-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Investment Opportunity</div>
            
            <h2>Investment Offer & Returns</h2>

            <div class="investment-content">
                <div class="investment-details">
                    <div class="detail-card primary slide-up" style="animation-delay: 0.1s;">
                        <h4 class="detail-title primary">💰 Capital Raise</h4>
                        <p class="detail-value">₹10 lakh (₹5 lakh minimum entry)</p>
                    </div>

                    <div class="detail-card blue slide-up" style="animation-delay: 0.2s;">
                        <h4 class="detail-title blue">🔒 Lock-in Period</h4>
                        <p class="detail-value">3 months with monthly progress reports</p>
                    </div>

                    <div class="detail-card purple slide-up" style="animation-delay: 0.3s;">
                        <h4 class="detail-title purple">📈 Expected ROI</h4>
                        <p class="detail-value">30%+ returns in 6 months</p>
                    </div>

                    <div class="detail-card orange slide-up" style="animation-delay: 0.4s;">
                        <h4 class="detail-title orange">🎯 Revenue Target</h4>
                        <p class="detail-value">₹30L revenue in 6 months</p>
                    </div>
                </div>

                <div class="chart-container slide-up" style="animation-delay: 0.5s;">
                    <div class="roi-chart">
                        ROI Growth Chart
                    </div>
                </div>
            </div>

            <div class="why-invest slide-up" style="animation-delay: 0.6s;">
                <h3>Why Invest in Whamart?</h3>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <div class="benefit-icon">🚀</div>
                        <div class="benefit-text">Ready to Launch</div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">💡</div>
                        <div class="benefit-text">Proven Market Need</div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">📊</div>
                        <div class="benefit-text">Clear Revenue Model</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide10.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide8.html';
        }
    </script>
</body>
</html>
