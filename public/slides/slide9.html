<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 9: Investment Offer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--accent) 0%, var(--primary) 50%, var(--blue) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }


        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 2rem 1.5rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--accent) 0%, var(--primary) 50%, var(--blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .investment-overview {
            margin-bottom: 1.5rem;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .overview-card {
            background: var(--light-gray);
            padding: 1.25rem;
            border-radius: 0.75rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .overview-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(37, 211, 102, 0.15);
        }

        .overview-label {
            font-size: 0.9rem;
            color: var(--gray);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .overview-value {
            font-size: 1.1rem;
            font-weight: 700;
        }

        .overview-value.primary { color: var(--primary); }
        .overview-value.blue { color: var(--blue); }
        .overview-value.purple { color: var(--purple); }
        .overview-value.orange { color: var(--orange); }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-align: center;
        }

        .revenue-projection {
            margin-bottom: 1.5rem;
        }

        .projection-grid {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .projection-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: var(--light-gray);
            border-radius: 0.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
        }

        .projection-icon {
            font-size: 1.5rem;
            margin-right: 0.75rem;
            min-width: 30px;
        }

        .projection-text {
            font-size: 0.9rem;
            color: var(--dark);
            line-height: 1.4;
        }

        .investor-benefits {
            margin-bottom: 1rem;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .benefit-card {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: var(--light-gray);
            border-radius: 0.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
        }

        .benefit-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(37, 211, 102, 0.15);
        }

        .benefit-icon {
            font-size: 1.5rem;
            margin-right: 0.75rem;
            min-width: 30px;
        }

        .benefit-text {
            font-size: 0.9rem;
            color: var(--dark);
            font-weight: 500;
            line-height: 1.3;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1.5rem 1rem;
            }

            h2 {
                font-size: 1.6rem;
                margin-bottom: 1rem;
            }

            .section-title {
                font-size: 1.1rem;
                margin-bottom: 0.75rem;
            }

            .overview-grid,
            .benefits-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .overview-card,
            .benefit-card,
            .projection-item {
                padding: 0.75rem;
            }

            .overview-value {
                font-size: 1rem;
            }

            .projection-text,
            .benefit-text {
                font-size: 0.8rem;
            }

            .investment-overview,
            .revenue-projection {
                margin-bottom: 1rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Investment Opportunity</div>
            
            <h2>Investment Offer & ROI Potential</h2>

            <div class="investment-overview">
                <div class="overview-grid">
                    <div class="overview-card slide-up" style="animation-delay: 0.1s;">
                        <div class="overview-label">We Are Raising</div>
                        <div class="overview-value primary">₹10,00,000</div>
                    </div>
                    <div class="overview-card slide-up" style="animation-delay: 0.2s;">
                        <div class="overview-label">Minimum Entry</div>
                        <div class="overview-value blue">₹5,00,000</div>
                    </div>
                    <div class="overview-card slide-up" style="animation-delay: 0.3s;">
                        <div class="overview-label">Lock-In Period</div>
                        <div class="overview-value purple">3 Months</div>
                    </div>
                    <div class="overview-card slide-up" style="animation-delay: 0.4s;">
                        <div class="overview-label">Disbursement Model</div>
                        <div class="overview-value orange">Monthly updates + exit in 6 months</div>
                    </div>
                </div>
            </div>

            <div class="revenue-projection">
                <h3 class="section-title">Revenue Projection (6 Months)</h3>
                <div class="projection-grid">
                    <div class="projection-item slide-up" style="animation-delay: 0.5s;">
                        <span class="projection-icon">📊</span>
                        <span class="projection-text">10,000 users x ₹1500 = <strong>₹1.5 Cr potential revenue</strong></span>
                    </div>
                    <div class="projection-item slide-up" style="animation-delay: 0.6s;">
                        <span class="projection-icon">💸</span>
                        <span class="projection-text">Expenses (marketing, ops): <strong>₹30–40L</strong></span>
                    </div>
                    <div class="projection-item slide-up" style="animation-delay: 0.7s;">
                        <span class="projection-icon">💰</span>
                        <span class="projection-text">Estimated Net Profit: <strong>₹1.1 Cr</strong></span>
                    </div>
                </div>
            </div>

            <div class="investor-benefits">
                <h3 class="section-title">Investor Benefit</h3>
                <div class="benefits-grid">
                    <div class="benefit-card slide-up" style="animation-delay: 0.8s;">
                        <div class="benefit-icon">📈</div>
                        <div class="benefit-text">30%–40% ROI within 6 months</div>
                    </div>
                    <div class="benefit-card slide-up" style="animation-delay: 0.9s;">
                        <div class="benefit-icon">📋</div>
                        <div class="benefit-text">Monthly progress reports</div>
                    </div>
                    <div class="benefit-card slide-up" style="animation-delay: 1.0s;">
                        <div class="benefit-icon">🔍</div>
                        <div class="benefit-text">Transparent & performance-linked model</div>
                    </div>
                    <div class="benefit-card slide-up" style="animation-delay: 1.1s;">
                        <div class="benefit-icon">🔄</div>
                        <div class="benefit-text">Option to reinvest or exit with profits</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide10.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide8.html';
        }
    </script>
</body>
</html>
