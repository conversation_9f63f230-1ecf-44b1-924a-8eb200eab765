<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 6: Business Model</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--purple) 0%, var(--orange) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }

        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 2.5rem 2rem;
            position: relative;
            z-index: 1;
        }


        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--purple) 0%, var(--orange) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .pricing-plans {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .plan-card {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .plan-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(37, 211, 102, 0.15);
        }

        .plan-icon {
            font-size: 2rem;
            margin-bottom: 0.75rem;
            display: block;
        }

        .plan-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }

        .plan-price {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary);
        }

        .guarantee-box {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .guarantee-box p {
            margin: 0;
            font-size: 0.9rem;
            color: var(--dark);
            font-weight: 500;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-align: center;
        }

        .how-we-earn, .vendor-potential {
            margin-bottom: 1.5rem;
        }

        .earn-points, .potential-points {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .earn-item, .potential-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: var(--light-gray);
            border-radius: 0.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
        }

        .check-icon, .potential-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            min-width: 30px;
        }

        .earn-text, .potential-text {
            font-size: 0.85rem;
            color: var(--dark);
            line-height: 1.3;
        }

        .quote-box {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            text-align: center;
        }

        .quote-box p {
            font-size: 0.95rem;
            color: var(--dark);
            margin: 0;
            font-style: italic;
            line-height: 1.4;
        }

        .quote-box i {
            color: var(--primary);
            margin: 0 0.25rem;
        }



        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem 1rem;
            }

            h2 {
                font-size: 1.6rem;
                margin-bottom: 1.5rem;
            }

            .pricing-plans {
                grid-template-columns: 1fr;
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .plan-card {
                padding: 1rem;
            }

            .section-title {
                font-size: 1.1rem;
                margin-bottom: 0.75rem;
            }

            .earn-points, .potential-points {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .earn-item, .potential-item {
                padding: 0.5rem;
            }

            .earn-text, .potential-text {
                font-size: 0.8rem;
            }

            .how-we-earn, .vendor-potential {
                margin-bottom: 1rem;
            }

            .guarantee-box {
                padding: 0.75rem;
                margin-bottom: 1rem;
            }

            .guarantee-box p {
                font-size: 0.8rem;
            }

            .quote-box {
                padding: 0.75rem;
            }

            .quote-box p {
                font-size: 0.85rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Revenue Model</div>

            <h2>Subscription-Only Model – Fair, Profitable & Scalable</h2>

            <div class="pricing-plans">
                <div class="plan-card slide-up" style="animation-delay: 0.1s;">
                    <div class="plan-icon">🔹</div>
                    <div class="plan-title">Basic Plan</div>
                    <div class="plan-price">₹1500 / year</div>
                </div>

                <div class="plan-card slide-up" style="animation-delay: 0.2s;">
                    <div class="plan-icon">🔹</div>
                    <div class="plan-title">Standard Plan</div>
                    <div class="plan-price">₹2000 / year</div>
                </div>

                <div class="plan-card slide-up" style="animation-delay: 0.3s;">
                    <div class="plan-icon">🔹</div>
                    <div class="plan-title">Premium Plan</div>
                    <div class="plan-price">₹3000 / year</div>
                </div>
            </div>

            <div class="guarantee-box slide-up" style="animation-delay: 0.4s;">
                <p>🛡️ No setup fee | No hidden charges | No commission on sales</p>
            </div>

            <div class="how-we-earn">
                <h3 class="section-title">How Whamart Earns</h3>
                <div class="earn-points">
                    <div class="earn-item slide-up" style="animation-delay: 0.5s;">
                        <span class="check-icon">✅</span>
                        <span class="earn-text">One-time yearly subscription</span>
                    </div>
                    <div class="earn-item slide-up" style="animation-delay: 0.6s;">
                        <span class="check-icon">✅</span>
                        <span class="earn-text">100% revenue from vendor plans</span>
                    </div>
                    <div class="earn-item slide-up" style="animation-delay: 0.7s;">
                        <span class="check-icon">✅</span>
                        <span class="earn-text">No transaction charges, no deductions</span>
                    </div>
                    <div class="earn-item slide-up" style="animation-delay: 0.8s;">
                        <span class="check-icon">✅</span>
                        <span class="earn-text">Predictable, stable recurring income model</span>
                    </div>
                </div>
            </div>

            <div class="vendor-potential">
                <h3 class="section-title">Vendor Earning Potential</h3>
                <div class="potential-points">
                    <div class="potential-item slide-up" style="animation-delay: 0.9s;">
                        <span class="potential-icon">📈</span>
                        <span class="potential-text">Whamart enables 24x7 auto-selling via chat automation</span>
                    </div>
                    <div class="potential-item slide-up" style="animation-delay: 1.0s;">
                        <span class="potential-icon">💸</span>
                        <span class="potential-text">Daily earning potential: ₹500 – ₹3500</span>
                    </div>
                    <div class="potential-item slide-up" style="animation-delay: 1.1s;">
                        <span class="potential-icon">📅</span>
                        <span class="potential-text">Monthly earning potential: ₹15,000 – ₹1,00,000</span>
                    </div>
                    <div class="potential-item slide-up" style="animation-delay: 1.2s;">
                        <span class="potential-icon">💰</span>
                        <span class="potential-text">Even the ₹1500 plan pays back in 1–2 days</span>
                    </div>
                </div>
            </div>

            <div class="quote-box slide-up" style="animation-delay: 1.3s;">
                <p>
                    <i class="fas fa-quote-left"></i>
                    <strong>Vendors run their business 24x7, even when they sleep — we just charge ₹4/day to make that possible.</strong>
                    <i class="fas fa-quote-right"></i>
                </p>
            </div>


        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide7.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide5.html';
        }
    </script>
</body>
</html>
