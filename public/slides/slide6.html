<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 6: Business Model</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--purple) 0%, var(--orange) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
        }

        .slide-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
        }

        .content-wrapper {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 2rem;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            max-width: 1200px;
            width: 100%;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .content-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--purple), var(--orange));
            border-radius: 2rem 2rem 0 0;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, var(--purple) 0%, var(--orange) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .revenue-models {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .model-card {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 1.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(37, 211, 102, 0.15);
        }

        .model-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }

        .model-icon.blue {
            background: linear-gradient(135deg, var(--blue), var(--purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .model-icon.purple {
            background: linear-gradient(135deg, var(--purple), var(--orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .model-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--dark);
        }

        .model-price {
            font-size: 2rem;
            font-weight: 800;
            margin: 1rem 0;
        }

        .model-price.primary { color: var(--primary); }
        .model-price.blue { color: var(--blue); }
        .model-price.purple { color: var(--purple); }

        .model-desc {
            font-size: 1rem;
            color: var(--gray);
            line-height: 1.5;
        }

        .revenue-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }

        .chart-container {
            background: var(--light-gray);
            border-radius: 1.5rem;
            padding: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
        }

        .chart-placeholder {
            width: 250px;
            height: 250px;
            border-radius: 1rem;
            background: linear-gradient(45deg, 
                var(--primary) 0% 25%, 
                var(--blue) 25% 50%, 
                var(--purple) 50% 75%, 
                var(--orange) 75% 100%);
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .projection-card {
            background: rgba(37, 211, 102, 0.1);
            padding: 2rem;
            border-radius: 1.5rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
            text-align: center;
        }

        .projection-title {
            color: var(--primary);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .projection-amount {
            font-size: 3rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .projection-timeline {
            color: var(--gray);
            margin-bottom: 1rem;
        }

        .projection-detail {
            color: var(--gray);
            font-size: 0.9rem;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }
            
            h2 {
                font-size: 2rem;
            }
            
            .revenue-models {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .revenue-section {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .chart-placeholder {
                width: 200px;
                height: 200px;
            }
            
            .projection-amount {
                font-size: 2.5rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Revenue Model</div>
            
            <h2>How We Make Money</h2>

            <div class="revenue-models">
                <div class="model-card slide-up" style="animation-delay: 0.1s;">
                    <i class="fas fa-calendar-alt model-icon"></i>
                    <div class="model-title">Annual Subscription</div>
                    <div class="model-price primary">₹1,500 - ₹3,000</div>
                    <div class="model-desc">Simple yearly plans based on features needed</div>
                </div>

                <div class="model-card slide-up" style="animation-delay: 0.2s;">
                    <i class="fas fa-plus-circle model-icon blue"></i>
                    <div class="model-title">Add-on Services</div>
                    <div class="model-price blue">Phase 2</div>
                    <div class="model-desc">AI Chatbot, Advanced CRM, Analytics</div>
                </div>

                <div class="model-card slide-up" style="animation-delay: 0.3s;">
                    <i class="fas fa-handshake model-icon purple"></i>
                    <div class="model-title">Zero Commission</div>
                    <div class="model-price purple">100%</div>
                    <div class="model-desc">Vendors keep all their profits</div>
                </div>
            </div>

            <div class="revenue-section">
                <div class="chart-container slide-up" style="animation-delay: 0.4s;">
                    <div class="chart-placeholder">
                        Revenue Growth Chart
                    </div>
                </div>

                <div class="projection-card slide-up" style="animation-delay: 0.5s;">
                    <h3 class="projection-title">Revenue Projection</h3>
                    <div class="projection-amount">₹30L</div>
                    <div class="projection-timeline">in 6 months</div>
                    <div class="projection-detail">From 2000+ active users</div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide7.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide5.html';
        }
    </script>
</body>
</html>
