<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 4: Market & TAM</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--accent) 0%, var(--purple) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: white;
        }

        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 4rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, var(--accent) 0%, var(--purple) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .market-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }

        .chart-container {
            background: var(--light-gray);
            border-radius: 1.5rem;
            padding: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
        }

        .chart-placeholder {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: conic-gradient(
                var(--primary) 0deg 126deg,
                var(--blue) 126deg 234deg,
                var(--purple) 234deg 288deg,
                var(--orange) 288deg 360deg
            );
            margin: 0 auto 1rem;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chart-center {
            width: 150px;
            height: 150px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .chart-total {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary);
        }

        .chart-label {
            font-size: 0.9rem;
            color: var(--gray);
            font-weight: 500;
        }

        .segments-list {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .segment-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: var(--light-gray);
            border-radius: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
        }

        .segment-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(37, 211, 102, 0.15);
        }

        .segment-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            width: 40px;
            text-align: center;
        }

        .segment-icon.kirana { color: var(--primary); }
        .segment-icon.freelancer { color: var(--blue); }
        .segment-icon.services { color: var(--purple); }
        .segment-icon.digital { color: var(--orange); }

        .segment-info h4 {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.25rem;
        }

        .segment-info p {
            color: var(--gray);
            font-size: 0.9rem;
        }

        .focus-box {
            grid-column: 1 / -1;
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
            margin-top: 2rem;
        }

        .focus-box p {
            font-size: 1.1rem;
            color: var(--dark);
            margin: 0;
        }

        .focus-box i {
            color: var(--primary);
            margin-right: 0.5rem;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }
            
            h2 {
                font-size: 2rem;
            }
            
            .market-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .chart-placeholder {
                width: 250px;
                height: 250px;
            }
            
            .chart-center {
                width: 120px;
                height: 120px;
            }
            
            .chart-total {
                font-size: 1.5rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Market Analysis</div>
            
            <h2>Our Target Audience & TAM</h2>

            <div class="market-content">
                <div class="chart-container slide-up" style="animation-delay: 0.1s;">
                    <div class="chart-placeholder">
                        <div class="chart-center">
                            <div class="chart-total">8Cr+</div>
                            <div class="chart-label">Total Market</div>
                        </div>
                    </div>
                </div>

                <div class="segments-list">
                    <div class="segment-item slide-up" style="animation-delay: 0.2s;">
                        <i class="fas fa-store segment-icon kirana"></i>
                        <div class="segment-info">
                            <h4>Kirana Stores</h4>
                            <p>2.5 Cr+ neighborhood stores</p>
                        </div>
                    </div>
                    
                    <div class="segment-item slide-up" style="animation-delay: 0.3s;">
                        <i class="fas fa-user-tie segment-icon freelancer"></i>
                        <div class="segment-info">
                            <h4>Freelancers</h4>
                            <p>3 Cr+ service providers</p>
                        </div>
                    </div>
                    
                    <div class="segment-item slide-up" style="animation-delay: 0.4s;">
                        <i class="fas fa-cut segment-icon services"></i>
                        <div class="segment-info">
                            <h4>Local Services</h4>
                            <p>1.5 Cr+ tailors, salons, etc.</p>
                        </div>
                    </div>
                    
                    <div class="segment-item slide-up" style="animation-delay: 0.5s;">
                        <i class="fas fa-tags segment-icon digital"></i>
                        <div class="segment-info">
                            <h4>Digital Sellers</h4>
                            <p>1 Cr+ online sellers</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="focus-box slide-up" style="animation-delay: 0.6s;">
                <p>
                    <i class="fas fa-map-marker-alt"></i>
                    Our early focus is on Surat, Tier 2/3 cities where WhatsApp is already the most used business tool.
                </p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide5.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide3.html';
        }
    </script>
</body>
</html>
