<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 4: Market & TAM</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: white;
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: white;
        }

        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 2rem 1.5rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--accent) 0%, var(--purple) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .target-segments {
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-align: center;
        }

        .segments-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .segment-card {
            background: var(--light-gray);
            padding: 1rem;
            border-radius: 0.75rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .segment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(37, 211, 102, 0.15);
        }

        .segment-icon {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 0.5rem;
            display: block;
        }

        .segment-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.25rem;
        }

        .segment-desc {
            font-size: 0.8rem;
            color: var(--gray);
            line-height: 1.3;
        }

        .market-potential {
            margin-bottom: 1.5rem;
        }

        .potential-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 0.75rem;
            background: var(--light-gray);
            border-radius: 0.75rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(37, 211, 102, 0.1);
        }

        .stat-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-text {
            font-size: 0.85rem;
            color: var(--dark);
            line-height: 1.3;
        }

        .tagline-box {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            text-align: center;
        }

        .tagline-box p {
            font-size: 1rem;
            color: var(--dark);
            margin: 0;
            font-style: italic;
            line-height: 1.4;
        }

        .tagline-box i {
            color: var(--primary);
            margin: 0 0.25rem;
        }



        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1.5rem 1rem;
            }

            h2 {
                font-size: 1.6rem;
                margin-bottom: 1rem;
            }

            .section-title {
                font-size: 1.1rem;
                margin-bottom: 0.75rem;
            }

            .target-segments {
                margin-bottom: 1rem;
            }

            .segments-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
                margin-bottom: 1rem;
            }

            .segment-card {
                padding: 0.75rem;
                min-height: 100px;
            }

            .segment-icon {
                font-size: 1.5rem;
                margin-bottom: 0.25rem;
            }

            .segment-title {
                font-size: 0.9rem;
            }

            .segment-desc {
                font-size: 0.75rem;
            }

            .market-potential {
                margin-bottom: 1rem;
            }

            .potential-stats {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .stat-item {
                padding: 0.5rem;
                flex-direction: row;
                text-align: left;
            }

            .stat-icon {
                font-size: 1.25rem;
                margin-bottom: 0;
                margin-right: 0.5rem;
            }

            .stat-text {
                font-size: 0.8rem;
            }

            .tagline-box {
                padding: 0.75rem;
            }

            .tagline-box p {
                font-size: 0.9rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Target Market</div>

            <h2>Who Is Whamart For?</h2>

            <div class="target-segments">
                <h3 class="section-title">Target Business Segments</h3>
                <div class="segments-grid">
                    <div class="segment-card slide-up" style="animation-delay: 0.1s;">
                        <i class="fas fa-store segment-icon"></i>
                        <div class="segment-title">Retail Businesses</div>
                        <div class="segment-desc">Kirana stores, boutiques, tailors, beauty parlours</div>
                    </div>

                    <div class="segment-card slide-up" style="animation-delay: 0.2s;">
                        <i class="fas fa-user-tie segment-icon"></i>
                        <div class="segment-title">Freelancers</div>
                        <div class="segment-desc">Mehendi artists, tutors, astrologers</div>
                    </div>

                    <div class="segment-card slide-up" style="animation-delay: 0.3s;">
                        <i class="fas fa-tools segment-icon"></i>
                        <div class="segment-title">Service Providers</div>
                        <div class="segment-desc">Repair, event planners, etc.</div>
                    </div>

                    <div class="segment-card slide-up" style="animation-delay: 0.4s;">
                        <i class="fas fa-download segment-icon"></i>
                        <div class="segment-title">Digital Sellers</div>
                        <div class="segment-desc">Ebooks, courses, PDFs, art</div>
                    </div>
                </div>
            </div>

            <div class="market-potential">
                <h3 class="section-title">Market Potential</h3>
                <div class="potential-stats">
                    <div class="stat-item slide-up" style="animation-delay: 0.5s;">
                        <div class="stat-icon">🧩</div>
                        <div class="stat-text">India has over <strong>8 Cr+ micro & small businesses</strong></div>
                    </div>

                    <div class="stat-item slide-up" style="animation-delay: 0.6s;">
                        <div class="stat-icon">📲</div>
                        <div class="stat-text">Over <strong>40 Cr daily messages</strong> are sent on WhatsApp for business</div>
                    </div>

                    <div class="stat-item slide-up" style="animation-delay: 0.7s;">
                        <div class="stat-icon">📈</div>
                        <div class="stat-text">Huge gap in <strong>affordable, ready-to-use digital storefronts</strong></div>
                    </div>
                </div>
            </div>

            <div class="tagline-box slide-up" style="animation-delay: 0.8s;">
                <p>
                    <i class="fas fa-quote-left"></i>
                    <strong>A ₹1500/year solution for a ₹30,000+ earning opportunity per vendor</strong>
                    <i class="fas fa-quote-right"></i>
                </p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide5.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide3.html';
        }
    </script>
</body>
</html>
