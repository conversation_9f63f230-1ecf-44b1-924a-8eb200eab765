<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 11: Thank You</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 50%, var(--blue) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }


        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 4rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        .logo-container {
            margin-bottom: 2rem;
        }

        .logo-container img {
            height: 100px;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
        }

        h1 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 50%, var(--blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .subtitle {
            font-size: 1.5rem;
            color: var(--gray);
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .thank-you-message {
            font-size: 1.2rem;
            color: var(--dark);
            line-height: 1.6;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-section {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 1.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            margin-bottom: 2rem;
        }

        .cta-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .cta-btn {
            display: inline-flex;
            align-items: center;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .cta-btn.primary {
            background: var(--primary);
            color: white;
        }

        .cta-btn.primary:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .cta-btn.outline {
            background: transparent;
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        .cta-btn.outline:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.3);
        }

        .cta-btn i {
            margin-right: 0.5rem;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 2rem;
        }

        .contact-item {
            background: rgba(37, 211, 102, 0.1);
            padding: 1rem;
            border-radius: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        .contact-item i {
            color: var(--primary);
            margin-right: 0.5rem;
        }

        .contact-item span {
            color: var(--dark);
            font-weight: 500;
        }

        .final-message {
            margin-top: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--blue));
            border-radius: 1rem;
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }

            h1 {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1.2rem;
            }
            
            .thank-you-message {
                font-size: 1rem;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .contact-info {
                grid-template-columns: 1fr;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .bounce-in {
            animation: bounceIn 1.2s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Thank You</div>
            
            <div class="logo-container bounce-in">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
            </div>
            
            <h1>Thank You!</h1>
            <div class="subtitle">Let's Build the Future of Digital Commerce Together</div>
            
            <p class="thank-you-message">
                Thank you for your time and consideration. We're excited about the opportunity to partner with you 
                and revolutionize how local businesses connect with their customers through WhatsApp.
            </p>

            <div class="cta-section">
                <h3 class="cta-title">Ready to Invest?</h3>
                <div class="cta-buttons">
                    <a href="mailto:<EMAIL>" class="cta-btn primary">
                        <i class="fas fa-envelope"></i>
                        Contact Us
                    </a>
                    <a href="https://wa.me/919876543210" class="cta-btn outline">
                        <i class="fab fa-whatsapp"></i>
                        WhatsApp
                    </a>
                </div>
            </div>

            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="fab fa-whatsapp"></i>
                    <span>+91 98765 43210</span>
                </div>
            </div>

            <div class="final-message">
                "Empowering 8 Crore+ businesses to go digital, one WhatsApp store at a time."
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" disabled>
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            // Last slide, no next
        }
        
        function previousSlide() {
            window.location.href = 'slide10.html';
        }
    </script>
</body>
</html>
