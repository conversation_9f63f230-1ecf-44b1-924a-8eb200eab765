<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 2: Problem Statement</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--accent) 0%, var(--primary) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: white;
        }

        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 4rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .problems-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .problem-card {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 1.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .problem-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(37, 211, 102, 0.15);
            border-color: rgba(37, 211, 102, 0.3);
        }

        .problem-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }

        .problem-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }

        .problem-text {
            font-size: 1rem;
            color: var(--gray);
            line-height: 1.5;
        }

        .opportunity-box {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
            margin-top: 2rem;
        }

        .opportunity-box p {
            font-size: 1.1rem;
            color: var(--dark);
            margin: 0;
        }

        .opportunity-box i {
            color: #FD7E14;
            margin-right: 0.5rem;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }
            
            h2 {
                font-size: 2rem;
            }
            
            .problems-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .problem-card {
                padding: 1.5rem;
            }
            
            .problem-icon {
                font-size: 2.5rem;
            }
            
            .problem-title {
                font-size: 1.1rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Problem Statement</div>
            
            <h2>The Problem We're Solving</h2>

            <div class="problems-grid">
                <div class="problem-card slide-up" style="animation-delay: 0.1s;">
                    <i class="fas fa-shop-slash problem-icon"></i>
                    <div class="problem-title">Offline Businesses</div>
                    <div class="problem-text">8 Cr+ local businesses still offline in India</div>
                </div>
                
                <div class="problem-card slide-up" style="animation-delay: 0.2s;">
                    <i class="fas fa-puzzle-piece problem-icon"></i>
                    <div class="problem-title">Complex Tools</div>
                    <div class="problem-text">Existing ecommerce tools are expensive or complicated</div>
                </div>
                
                <div class="problem-card slide-up" style="animation-delay: 0.3s;">
                    <i class="fab fa-whatsapp problem-icon"></i>
                    <div class="problem-title">WhatsApp API</div>
                    <div class="problem-text">WhatsApp API is not affordable or beginner-friendly</div>
                </div>
                
                <div class="problem-card slide-up" style="animation-delay: 0.4s;">
                    <i class="fas fa-credit-card problem-icon"></i>
                    <div class="problem-title">Payment Setup</div>
                    <div class="problem-text">Payment gateway setup is costly & takes time</div>
                </div>
            </div>

            <div class="opportunity-box slide-up" style="animation-delay: 0.5s;">
                <p>
                    <i class="fas fa-lightbulb"></i>
                    <strong>The Opportunity:</strong> Millions of small businesses need a simple, affordable way to go digital and reach customers through WhatsApp.
                </p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide3.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide1.html';
        }
    </script>
</body>
</html>
