<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Investor Presentation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
        }

        .presentation-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
        }

        .content-wrapper {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 2rem;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 1000px;
            width: 100%;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .content-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            border-radius: 2rem 2rem 0 0;
        }

        .logo-container {
            margin-bottom: 2rem;
        }

        .logo-container img {
            height: 100px;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
        }

        h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .subtitle {
            font-size: 1.3rem;
            color: var(--gray);
            margin-bottom: 3rem;
            font-weight: 500;
        }

        .slides-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .slide-card {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .slide-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(37, 211, 102, 0.15);
            border-color: rgba(37, 211, 102, 0.3);
        }

        .slide-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            color: white;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            margin-left: auto;
            margin-right: auto;
        }

        .slide-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .slide-desc {
            font-size: 0.9rem;
            color: var(--gray);
            line-height: 1.4;
        }

        .start-presentation {
            background: var(--primary);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .start-presentation:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .presentation-info {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: left;
        }

        .info-title {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .info-text {
            color: var(--gray);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .navigation-help {
            margin-top: 1rem;
            padding: 1rem;
            background: var(--light-gray);
            border-radius: 0.5rem;
            font-size: 0.85rem;
            color: var(--gray);
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 2rem;
                margin: 1rem;
            }
            
            h1 {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
            
            .slides-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
            }
            
            .slide-card {
                padding: 1rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="content-wrapper fade-in">
            <div class="logo-container">
                <img src="WhaMart_Logo.png" alt="Whamart Logo">
            </div>
            
            <h1>Whamart Investor Presentation</h1>
            <div class="subtitle">Empowering Bharat's Local Businesses Digitally</div>

            <div class="slides-grid">
                <a href="slides/slide1.html" class="slide-card slide-up" style="animation-delay: 0.1s;">
                    <div class="slide-number">1</div>
                    <div class="slide-title">Title Slide</div>
                    <div class="slide-desc">Introduction to Whamart and key metrics</div>
                </a>

                <a href="slides/slide2.html" class="slide-card slide-up" style="animation-delay: 0.2s;">
                    <div class="slide-number">2</div>
                    <div class="slide-title">Problem Statement</div>
                    <div class="slide-desc">The challenges facing local businesses</div>
                </a>

                <a href="slides/slide3.html" class="slide-card slide-up" style="animation-delay: 0.3s;">
                    <div class="slide-number">3</div>
                    <div class="slide-title">Our Solution</div>
                    <div class="slide-desc">Whamart's features and capabilities</div>
                </a>

                <a href="slides/slide4.html" class="slide-card slide-up" style="animation-delay: 0.4s;">
                    <div class="slide-number">4</div>
                    <div class="slide-title">Market & TAM</div>
                    <div class="slide-desc">Target audience and market analysis</div>
                </a>

                <a href="slides/slide5.html" class="slide-card slide-up" style="animation-delay: 0.5s;">
                    <div class="slide-number">5</div>
                    <div class="slide-title">Competition Analysis</div>
                    <div class="slide-desc">How we compare to competitors</div>
                </a>

                <a href="slides/slide6.html" class="slide-card slide-up" style="animation-delay: 0.6s;">
                    <div class="slide-number">6</div>
                    <div class="slide-title">Business Model</div>
                    <div class="slide-desc">Revenue streams and projections</div>
                </a>

                <a href="slides/slide7.html" class="slide-card slide-up" style="animation-delay: 0.7s;">
                    <div class="slide-number">7</div>
                    <div class="slide-title">Investment & Capital Use</div>
                    <div class="slide-desc">How we'll use the ₹10L funding</div>
                </a>

                <a href="slides/slide8.html" class="slide-card slide-up" style="animation-delay: 0.8s;">
                    <div class="slide-number">8</div>
                    <div class="slide-title">Timeline & Readiness</div>
                    <div class="slide-desc">Product readiness and launch timeline</div>
                </a>

                <a href="slides/slide9.html" class="slide-card slide-up" style="animation-delay: 0.9s;">
                    <div class="slide-number">9</div>
                    <div class="slide-title">Investment Offer</div>
                    <div class="slide-desc">Investment terms and expected returns</div>
                </a>

                <a href="slides/slide10.html" class="slide-card slide-up" style="animation-delay: 1.0s;">
                    <div class="slide-number">10</div>
                    <div class="slide-title">Team & Contact</div>
                    <div class="slide-desc">Meet the team and contact information</div>
                </a>

                <a href="slides/slide11.html" class="slide-card slide-up" style="animation-delay: 1.1s;">
                    <div class="slide-number">11</div>
                    <div class="slide-title">Thank You</div>
                    <div class="slide-desc">Closing remarks and next steps</div>
                </a>
            </div>

            <a href="slides/slide1.html" class="start-presentation slide-up" style="animation-delay: 1.2s;">
                <i class="fas fa-play"></i>
                Start Presentation
            </a>

            <div class="presentation-info slide-up" style="animation-delay: 1.3s;">
                <div class="info-title">Presentation Overview</div>
                <div class="info-text">
                    This presentation covers Whamart's business model, market opportunity, and investment proposal. 
                    Each slide is designed with your homepage's modern aesthetic using Poppins font and WhatsApp-inspired colors.
                </div>
                <div class="navigation-help">
                    <strong>Navigation:</strong> Use the arrow buttons on each slide to navigate, or click any slide above to jump directly to it.
                </div>
            </div>
        </div>
    </div>
</body>
</html>
